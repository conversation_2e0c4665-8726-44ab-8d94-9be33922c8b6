package com.knet.payment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.enums.KnetPaymentGroupStatus;
import com.knet.common.enums.PaymentChannel;
import com.knet.common.enums.WalletRecordType;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.NumberUtils;
import com.knet.common.utils.RandomStrUtil;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.res.CreatePaymentResponse;
import com.knet.payment.model.entity.SysPaymentFlow;
import com.knet.payment.model.entity.SysPaymentGroup;
import com.knet.payment.model.entity.SysUserWallet;
import com.knet.payment.mq.producer.PaymentMessageProducer;
import com.knet.payment.service.IPaymentService;
import com.knet.payment.service.ISysPaymentFlowService;
import com.knet.payment.service.ISysPaymentGroupService;
import com.knet.payment.service.ISysUserWalletService;
import com.knet.payment.service.ISysWalletRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:50
 * @description: 支付服务实现类
 */
@Slf4j
@Service
public class PaymentServiceImpl implements IPaymentService {

    @Resource
    private RandomStrUtil randomStrUtil;
    
    @Resource
    private ISysPaymentGroupService paymentGroupService;
    
    @Resource
    private ISysPaymentFlowService paymentFlowService;
    
    @Resource
    private ISysUserWalletService userWalletService;
    
    @Resource
    private ISysWalletRecordService walletRecordService;
    
    @Resource
    private PaymentMessageProducer paymentMessageProducer;

    /**
     * 创建支付
     *
     * @param request 创建支付请求
     * @return 创建支付响应
     */
    @DistributedLock(key = "'payment:create:'+#request.userId+':'+#request.orderId", expire = 5)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreatePaymentResponse createPayment(CreatePaymentRequest request) {
        CreatePaymentRequest.checkCreatePaymentRequest(request);
        
        Long userId = request.getUserId();
        String orderId = request.getOrderId();
        BigDecimal amount = request.getAmount();
        PaymentChannel paymentChannel = request.getPaymentChannel();
        
        log.info("创建支付开始: userId={}, orderId={}, amount={}, channel={}", 
                userId, orderId, amount, paymentChannel.getName());
        
        try {
            // 1. 生成支付组ID和支付流水ID
            String groupId = randomStrUtil.getPaymentGroupId();
            String paymentId = randomStrUtil.getPaymentId();
            
            // 2. 创建支付组
            SysPaymentGroup paymentGroup = new SysPaymentGroup()
                    .setGroupId(groupId)
                    .setUserId(userId)
                    .setOrderId(orderId)
                    .setTotalAmount(amount)
                    .setPaidAmount(BigDecimal.ZERO)
                    .setStatus(KnetPaymentGroupStatus.UNFINISHED);
            
            boolean groupSaved = paymentGroupService.save(paymentGroup);
            if (!groupSaved) {
                throw new ServiceException("支付组创建失败");
            }
            
            // 3. 创建支付流水
            SysPaymentFlow paymentFlow = new SysPaymentFlow()
                    .setPaymentId(paymentId)
                    .setGroupId(groupId)
                    .setPayChannel(paymentChannel.getCode())
                    .setAmount(amount)
                    .setStatus(KnetPaymentFlowStatus.PENDING);
            
            boolean flowSaved = paymentFlowService.save(paymentFlow);
            if (!flowSaved) {
                throw new ServiceException("支付流水创建失败");
            }
            
            // 4. 根据支付渠道处理支付
            CreatePaymentResponse response = processPaymentByChannel(paymentFlow, request);
            
            log.info("创建支付成功: paymentId={}, groupId={}, status={}", 
                    paymentId, groupId, response.getStatus());
            
            return response;
            
        } catch (Exception e) {
            log.error("创建支付失败: userId={}, orderId={}, error={}", userId, orderId, e.getMessage(), e);
            throw new ServiceException("创建支付失败: " + e.getMessage());
        }
    }

    /**
     * 根据支付渠道处理支付
     */
    private CreatePaymentResponse processPaymentByChannel(SysPaymentFlow paymentFlow, CreatePaymentRequest request) {
        PaymentChannel channel = PaymentChannel.fromCode(paymentFlow.getPayChannel());
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder = CreatePaymentResponse.builder()
                .groupId(paymentFlow.getGroupId())
                .paymentId(paymentFlow.getPaymentId())
                .userId(request.getUserId())
                .orderId(request.getOrderId())
                .amount(NumberUtils.formatDecimal(paymentFlow.getAmount()))
                .paymentChannel(channel.getName())
                .createTime(currentTime);
        
        try {
            switch (channel) {
                case WALLET:
                    return processWalletPayment(paymentFlow, request, responseBuilder);
                case CREDIT_CARD:
                case PAYPAL:
                case BANK_TRANSFER:
                case ALIPAY:
                case WECHAT_PAY:
                    return processThirdPartyPayment(paymentFlow, request, responseBuilder, channel);
                default:
                    throw new ServiceException("不支持的支付渠道: " + channel.getName());
            }
        } catch (Exception e) {
            // 支付失败，发送MQ消息
            sendPaymentFailMessage(paymentFlow, request, e.getMessage());
            throw e;
        }
    }

    /**
     * 处理钱包支付
     */
    private CreatePaymentResponse processWalletPayment(SysPaymentFlow paymentFlow, 
                                                     CreatePaymentRequest request,
                                                     CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder) {
        Long userId = request.getUserId();
        BigDecimal amount = paymentFlow.getAmount();
        
        // 查询用户钱包
        LambdaQueryWrapper<SysUserWallet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserWallet::getUserId, userId);
        SysUserWallet userWallet = userWalletService.getOne(queryWrapper);
        
        if (BeanUtil.isEmpty(userWallet)) {
            throw new ServiceException("用户钱包不存在");
        }
        
        if (userWallet.getBalance().compareTo(amount) < 0) {
            throw new ServiceException("钱包余额不足");
        }
        
        // 扣减钱包余额
        BigDecimal newBalance = userWallet.getBalance().subtract(amount);
        userWallet.setBalance(newBalance);
        boolean updated = userWalletService.updateById(userWallet);
        if (!updated) {
            throw new ServiceException("钱包余额扣减失败");
        }
        
        // 创建钱包记录
        String recordId = walletRecordService.createWalletRecord(
                userId, amount, WalletRecordType.PAYMENT_DEDUCTION, 
                paymentFlow.getPaymentId(), request.getOrderId(), request.getRemark());
        
        // 更新支付流水状态为成功
        paymentFlow.setStatus(KnetPaymentFlowStatus.SUCCESS);
        paymentFlowService.updateById(paymentFlow);
        
        // 更新支付组状态
        updatePaymentGroupStatus(paymentFlow.getGroupId(), amount);
        
        return responseBuilder
                .status(KnetPaymentFlowStatus.SUCCESS.getName())
                .build();
    }

    /**
     * 处理第三方支付
     */
    private CreatePaymentResponse processThirdPartyPayment(SysPaymentFlow paymentFlow,
                                                         CreatePaymentRequest request,
                                                         CreatePaymentResponse.CreatePaymentResponseBuilder responseBuilder,
                                                         PaymentChannel channel) {
        // 更新支付流水状态为支付中
        paymentFlow.setStatus(KnetPaymentFlowStatus.IN_PROGRESS);
        paymentFlowService.updateById(paymentFlow);
        
        // 模拟第三方支付接口调用
        // 实际实现中这里应该调用具体的第三方支付接口
        String paymentUrl = generatePaymentUrl(paymentFlow, channel);
        
        return responseBuilder
                .status(KnetPaymentFlowStatus.IN_PROGRESS.getName())
                .paymentUrl(paymentUrl)
                .build();
    }

    /**
     * 生成支付链接（模拟）
     */
    private String generatePaymentUrl(SysPaymentFlow paymentFlow, PaymentChannel channel) {
        // 实际实现中应该调用第三方支付接口获取支付链接
        return String.format("https://pay.%s.com/pay?paymentId=%s", 
                channel.getName().toLowerCase(), paymentFlow.getPaymentId());
    }

    /**
     * 更新支付组状态
     */
    private void updatePaymentGroupStatus(String groupId, BigDecimal paidAmount) {
        LambdaQueryWrapper<SysPaymentGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPaymentGroup::getGroupId, groupId);
        SysPaymentGroup paymentGroup = paymentGroupService.getOne(queryWrapper);
        
        if (paymentGroup != null) {
            BigDecimal newPaidAmount = paymentGroup.getPaidAmount().add(paidAmount);
            paymentGroup.setPaidAmount(newPaidAmount);
            
            // 如果已支付金额等于总金额，则标记为已完成
            if (newPaidAmount.compareTo(paymentGroup.getTotalAmount()) >= 0) {
                paymentGroup.setStatus(KnetPaymentGroupStatus.COMPLETED);
            }
            
            paymentGroupService.updateById(paymentGroup);
        }
    }

    /**
     * 发送支付失败消息
     */
    private void sendPaymentFailMessage(SysPaymentFlow paymentFlow, CreatePaymentRequest request, String errorMessage) {
        try {
            // 更新支付流水状态为失败
            paymentFlow.setStatus(KnetPaymentFlowStatus.FAILED);
            paymentFlowService.updateById(paymentFlow);
            
            PaymentChannel channel = PaymentChannel.fromCode(paymentFlow.getPayChannel());
            paymentMessageProducer.sendPaymentFailMessage(
                    paymentFlow.getPaymentId(),
                    request.getUserId(),
                    request.getOrderId(),
                    NumberUtils.formatDecimal(paymentFlow.getAmount()),
                    channel.getName(),
                    errorMessage
            );
        } catch (Exception e) {
            log.error("发送支付失败消息异常: paymentId={}, error={}", 
                    paymentFlow.getPaymentId(), e.getMessage());
        }
    }

    @Override
    public CreatePaymentResponse queryPaymentStatus(String paymentId) {
        // TODO: 实现查询支付状态
        throw new ServiceException("查询支付状态功能待实现");
    }

    @Override
    public boolean cancelPayment(String paymentId) {
        // TODO: 实现取消支付
        throw new ServiceException("取消支付功能待实现");
    }
}
