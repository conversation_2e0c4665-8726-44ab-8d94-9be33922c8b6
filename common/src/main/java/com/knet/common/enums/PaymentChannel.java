package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:30
 * @description: 支付渠道枚举
 */
@Getter
@AllArgsConstructor
public enum PaymentChannel {

    /**
     * 钱包支付
     */
    WALLET(1, "WALLET", "钱包支付"),
    /**
     * 信用卡支付
     */
    CREDIT_CARD(2, "CREDIT_CARD", "信用卡支付"),
    /**
     * PayPal支付
     */
    PAYPAL(3, "PAYPAL", "PayPal支付"),
    /**
     * 银行转账
     */
    BANK_TRANSFER(4, "BANK_TRANSFER", "银行转账"),
    /**
     * 支付宝
     */
    ALIPAY(5, "ALIPAY", "支付宝"),
    /**
     * 微信支付
     */
    WECHAT_PAY(6, "WECHAT_PAY", "微信支付");

    /**
     * 渠道代码
     */
    private final Integer code;

    /**
     * 渠道名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;

    /**
     * 描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static PaymentChannel fromCode(int code) {
        for (PaymentChannel channel : PaymentChannel.values()) {
            if (channel.getCode() == code) {
                return channel;
            }
        }
        throw new IllegalArgumentException("Unknown payment channel code " + code);
    }

    public static PaymentChannel fromName(String name) {
        for (PaymentChannel channel : PaymentChannel.values()) {
            if (channel.getName().equals(name)) {
                return channel;
            }
        }
        throw new IllegalArgumentException("Unknown payment channel name " + name);
    }
}
