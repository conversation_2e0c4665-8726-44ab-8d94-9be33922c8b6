package com.knet.common.dto;

import com.knet.common.enums.OperationResult;
import com.knet.common.enums.UserOperationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/20 20:00
 * @description: 用户操作记录消息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserOperationRecordMessage implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 操作者ID
     */
    private Long operatorId;

    /**
     * 操作者类型（USER-用户，ADMIN-管理员，SYSTEM-系统）
     */
    private String operatorType;

    /**
     * 操作类型
     */
    private UserOperationType operationType;

    /**
     * 操作结果
     */
    private OperationResult operationResult;

    /**
     * 操作描述
     */
    private String operationDesc;

    /**
     * 操作详情（JSON格式存储具体的操作数据）
     */
    private String operationDetail;

    /**
     * 客户端IP地址
     */
    private String clientIp;

    /**
     * 用户代理（浏览器信息）
     */
    private String userAgent;

    /**
     * 关联业务ID（如订单ID、支付ID等）
     */
    private String businessId;

    /**
     * 关联业务类型（如ORDER、PAYMENT、WALLET等）
     */
    private String businessType;

    /**
     * 错误信息（操作失败时记录）
     */
    private String errorMessage;

    /**
     * 备注信息
     */
    private String remarks;
}
